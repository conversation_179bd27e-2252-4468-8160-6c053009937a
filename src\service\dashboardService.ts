import { apiClient } from '../api';
import { 
  DashboardStatsResponse, 
  TransactionsResponse, 
  BookingsResponse, 
  UserProfileResponse,
  DashboardFilters,
  DashboardStats,
  Transaction,
  BookingData,
  UserProfile
} from '../feature-module/frontend/Customer/Dashboard/types/dashboardTypes';

/**
 * Get dashboard statistics for the current user
 */
export const getDashboardStats = async (userId: string): Promise<DashboardStats> => {
  try {
    console.log(`Fetching dashboard stats for user: ${userId}`);
    
    const response = await apiClient.get<DashboardStatsResponse>(`/api/v1/dashboard/stats/${userId}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to fetch dashboard statistics');
  } catch (error: any) {
    console.error('Error fetching dashboard stats:', error);
    
    // Return mock data as fallback for development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Using mock dashboard stats data');
      return {
        totalOrders: 27,
        totalSpend: 2500,
        walletBalance: 200,
        totalSavings: 354,
        orderGrowth: 16,
        spendGrowth: 5,
        walletGrowth: 5,
        savingsGrowth: 16
      };
    }
    
    throw error;
  }
};

/**
 * Get recent transactions for the current user
 */
export const getRecentTransactions = async (
  userId: string, 
  filters?: DashboardFilters
): Promise<Transaction[]> => {
  try {
    console.log(`Fetching recent transactions for user: ${userId}`, filters);
    
    const params = new URLSearchParams();
    params.append('userId', userId);
    params.append('limit', (filters?.limit || 5).toString());
    
    if (filters?.transactionType) {
      params.append('type', filters.transactionType);
    }
    
    if (filters?.dateRange) {
      params.append('startDate', filters.dateRange.start);
      params.append('endDate', filters.dateRange.end);
    }
    
    const response = await apiClient.get<TransactionsResponse>(`/api/v1/transactions?${params.toString()}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data.transactions;
    }
    
    throw new Error(response.data.message || 'Failed to fetch transactions');
  } catch (error: any) {
    console.error('Error fetching recent transactions:', error);
    
    // Return mock data as fallback for development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Using mock transactions data');
      return [
        {
          id: '1',
          type: 'Service Booking',
          amount: 280,
          currency: 'USD',
          status: 'In Process',
          date: '2024-01-15T09:12:00Z',
          transactionId: 'TXN001'
        },
        {
          id: '2',
          type: 'Service Refund',
          amount: 395,
          currency: 'USD',
          status: 'Completed',
          date: '2024-01-14T16:36:00Z',
          transactionId: 'TXN002'
        },
        {
          id: '3',
          type: 'Wallet Topup',
          amount: 1000,
          currency: 'USD',
          status: 'Pending',
          date: '2024-01-13T10:00:00Z',
          transactionId: 'TXN003'
        },
        {
          id: '4',
          type: 'Service Booking',
          amount: 598.65,
          currency: 'USD',
          status: 'Cancelled',
          date: '2024-01-12T11:17:00Z',
          transactionId: 'TXN004'
        }
      ];
    }
    
    throw error;
  }
};

/**
 * Get recent bookings for the current user
 */
export const getRecentBookings = async (
  userId: string, 
  filters?: DashboardFilters
): Promise<BookingData[]> => {
  try {
    console.log(`Fetching recent bookings for user: ${userId}`, filters);
    
    const params = new URLSearchParams();
    params.append('userId', userId);
    params.append('limit', (filters?.limit || 5).toString());
    
    if (filters?.bookingStatus) {
      params.append('status', filters.bookingStatus);
    }
    
    if (filters?.dateRange) {
      params.append('startDate', filters.dateRange.start);
      params.append('endDate', filters.dateRange.end);
    }
    
    const response = await apiClient.get<BookingsResponse>(`/api/v1/bookings?${params.toString()}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data.bookings;
    }
    
    throw new Error(response.data.message || 'Failed to fetch bookings');
  } catch (error: any) {
    console.error('Error fetching recent bookings:', error);
    
    // Return mock data as fallback for development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Using mock bookings data');
      return [
        {
          id: '1',
          bookingId: 'BK001',
          service: 'Computer Repair',
          customerName: 'John Smith',
          customerEmail: '<EMAIL>',
          amount: 150,
          currency: 'USD',
          status: 'In Process',
          date: '2024-01-15T10:00:00Z',
          canCancel: true,
          canReschedule: true
        },
        {
          id: '2',
          bookingId: 'BK002',
          service: 'Car Repair',
          customerName: 'Timothy',
          customerEmail: '<EMAIL>',
          amount: 300,
          currency: 'USD',
          status: 'Pending',
          date: '2024-01-20T14:00:00Z',
          canCancel: true,
          canReschedule: true
        },
        {
          id: '3',
          bookingId: 'BK003',
          service: 'Interior Designing',
          customerName: 'Jordan',
          customerEmail: '<EMAIL>',
          amount: 500,
          currency: 'USD',
          status: 'Completed',
          date: '2024-01-10T09:00:00Z',
          canCancel: false,
          canReschedule: false
        }
      ];
    }
    
    throw error;
  }
};

/**
 * Get user profile data
 */
export const getUserProfile = async (userId: string): Promise<UserProfile> => {
  try {
    console.log(`Fetching user profile for user: ${userId}`);
    
    const response = await apiClient.get<UserProfileResponse>(`/api/v1/user/${userId}`);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    // Try alternative response structure
    if (response.data) {
      return response.data as UserProfile;
    }
    
    throw new Error('Failed to fetch user profile');
  } catch (error: any) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

/**
 * Refresh all dashboard data
 */
export const refreshDashboardData = async (userId: string) => {
  try {
    console.log(`Refreshing dashboard data for user: ${userId}`);
    
    const [stats, transactions, bookings, profile] = await Promise.allSettled([
      getDashboardStats(userId),
      getRecentTransactions(userId),
      getRecentBookings(userId),
      getUserProfile(userId)
    ]);
    
    return {
      stats: stats.status === 'fulfilled' ? stats.value : null,
      transactions: transactions.status === 'fulfilled' ? transactions.value : [],
      bookings: bookings.status === 'fulfilled' ? bookings.value : [],
      profile: profile.status === 'fulfilled' ? profile.value : null,
      errors: {
        stats: stats.status === 'rejected' ? stats.reason?.message : null,
        transactions: transactions.status === 'rejected' ? transactions.reason?.message : null,
        bookings: bookings.status === 'rejected' ? bookings.reason?.message : null,
        profile: profile.status === 'rejected' ? profile.reason?.message : null
      }
    };
  } catch (error: any) {
    console.error('Error refreshing dashboard data:', error);
    throw error;
  }
};
