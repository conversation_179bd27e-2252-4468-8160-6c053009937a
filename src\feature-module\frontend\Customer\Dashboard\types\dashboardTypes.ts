// Dashboard Statistics Interface
export interface DashboardStats {
  totalOrders: number;
  totalSpend: number;
  walletBalance: number;
  totalSavings: number;
  orderGrowth?: number; // Percentage growth
  spendGrowth?: number;
  walletGrowth?: number;
  savingsGrowth?: number;
}

// Transaction Interface
export interface Transaction {
  id: string;
  type: 'Service Booking' | 'Service Refund' | 'Wallet Topup' | 'Wallet Withdrawal' | 'Payment';
  description?: string;
  amount: number;
  currency: string;
  status: 'Completed' | 'Pending' | 'In Process' | 'Cancelled' | 'Failed';
  date: string;
  bookingId?: string;
  serviceId?: string;
  providerId?: string;
  paymentMethod?: string;
  transactionId?: string;
}

// Booking Data Interface (for dashboard display)
export interface BookingData {
  id: string;
  bookingId: string;
  service: string;
  serviceName?: string;
  serviceId?: string;
  providerId?: string;
  providerName?: string;
  customerName?: string;
  customerEmail?: string;
  amount: number;
  currency: string;
  status: 'Pending' | 'Confirmed' | 'Finished' | 'Cancelled' | 'Completed' | 'Inprogress' | 'Rescheduled';
  date: string;
  location?: string;
  duration?: string;
  description?: string;
  serviceImages?: string[];
  canCancel?: boolean;
  canReschedule?: boolean;
}

// API Response Interfaces
export interface DashboardStatsResponse {
  success: boolean;
  data: DashboardStats;
  message?: string;
}

export interface TransactionsResponse {
  success: boolean;
  data: {
    transactions: Transaction[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

export interface BookingsResponse {
  success: boolean;
  data: {
    bookings: BookingData[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

// User Profile Interface
export interface UserProfile {
  id: string;
  name?: string;
  email?: string;
  profileImage?: string;
  profileImageUrl?: string;
  phone?: string;
  address?: string;
  walletBalance?: number;
}

export interface UserProfileResponse {
  success: boolean;
  data: UserProfile;
  message?: string;
}

// Dashboard Data Combined Interface
export interface DashboardData {
  stats: DashboardStats | null;
  recentTransactions: Transaction[];
  recentBookings: BookingData[];
  userProfile: UserProfile | null;
}

// Loading States Interface
export interface DashboardLoadingStates {
  stats: boolean;
  transactions: boolean;
  bookings: boolean;
  userProfile: boolean;
}

// Error States Interface
export interface DashboardErrors {
  stats: string | null;
  transactions: string | null;
  bookings: string | null;
  userProfile: string | null;
}

// Dashboard Filters Interface
export interface DashboardFilters {
  transactionType?: string;
  bookingStatus?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  limit?: number;
}

// Stat Card Configuration Interface
export interface StatCardConfig {
  title: string;
  value: string | number;
  percentage?: string;
  color: string;
  bg: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  loading?: boolean;
  error?: string | null;
}

// Dashboard Action Types
export type DashboardAction = 
  | 'view_all_transactions'
  | 'view_all_bookings'
  | 'cancel_booking'
  | 'reschedule_booking'
  | 'refresh_data'
  | 'view_booking_details'
  | 'view_transaction_details';

// Dashboard Navigation Interface
export interface DashboardNavigation {
  action: DashboardAction;
  data?: any;
  callback?: () => void;
}

// Status Color Mapping
export type StatusColorType = 'success' | 'warning' | 'primary' | 'danger' | 'default';

// Chart Data Interface (for future enhancements)
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface DashboardChartData {
  spending: ChartDataPoint[];
  bookings: ChartDataPoint[];
  wallet: ChartDataPoint[];
}
