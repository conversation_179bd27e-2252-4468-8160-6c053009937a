import { StatusColorType } from '../types/dashboardTypes';

/**
 * Format currency values for display
 */
export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    console.warn('Error formatting currency:', error);
    return `$${amount.toFixed(2)}`;
  }
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid
    }
    
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(date);
  } catch (error) {
    console.warn('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Format date for display (short version)
 */
export const formatDateShort = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return dateString;
    }
    
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
    }).format(date);
  } catch (error) {
    console.warn('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Get status color for chips and badges
 */
export const getStatusColor = (status: string): StatusColorType => {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'finished':
    case 'success':
      return 'success';
    case 'pending':
    case 'waiting':
      return 'warning';
    case 'in process':
    case 'inprogress':
    case 'confirmed':
    case 'processing':
      return 'primary';
    case 'cancelled':
    case 'failed':
    case 'rejected':
      return 'danger';
    case 'rescheduled':
      return 'warning';
    default:
      return 'default';
  }
};

/**
 * Format percentage for display
 */
export const formatPercentage = (value: number): string => {
  const sign = value >= 0 ? '+' : '';
  return `${sign}${value.toFixed(1)}%`;
};

/**
 * Get trend direction from percentage
 */
export const getTrendDirection = (percentage: number): 'up' | 'down' | 'neutral' => {
  if (percentage > 0) return 'up';
  if (percentage < 0) return 'down';
  return 'neutral';
};

/**
 * Get trend color class
 */
export const getTrendColor = (percentage: number): string => {
  if (percentage > 0) return 'text-green-500';
  if (percentage < 0) return 'text-red-500';
  return 'text-gray-500';
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Get relative time string (e.g., "2 hours ago")
 */
export const getRelativeTime = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
      return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
    }
    
    return formatDateShort(dateString);
  } catch (error) {
    console.warn('Error calculating relative time:', error);
    return formatDateShort(dateString);
  }
};

/**
 * Check if booking can be cancelled
 */
export const canCancelBooking = (status: string, date: string): boolean => {
  const bookingDate = new Date(date);
  const now = new Date();
  const hoursUntilBooking = (bookingDate.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  // Can cancel if status allows and booking is more than 24 hours away
  const cancellableStatuses = ['pending', 'confirmed'];
  return cancellableStatuses.includes(status.toLowerCase()) && hoursUntilBooking > 24;
};

/**
 * Check if booking can be rescheduled
 */
export const canRescheduleBooking = (status: string, date: string): boolean => {
  const bookingDate = new Date(date);
  const now = new Date();
  const hoursUntilBooking = (bookingDate.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  // Can reschedule if status allows and booking is more than 48 hours away
  const reschedulableStatuses = ['pending', 'confirmed'];
  return reschedulableStatuses.includes(status.toLowerCase()) && hoursUntilBooking > 48;
};

/**
 * Generate loading skeleton count based on data type
 */
export const getSkeletonCount = (type: 'stats' | 'transactions' | 'bookings'): number => {
  switch (type) {
    case 'stats':
      return 4;
    case 'transactions':
      return 4;
    case 'bookings':
      return 3;
    default:
      return 3;
  }
};

/**
 * Validate and sanitize user input
 */
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

/**
 * Generate unique key for React lists
 */
export const generateKey = (prefix: string, index: number, id?: string): string => {
  return `${prefix}-${id || index}-${Date.now()}`;
};

/**
 * Check if data is empty or null
 */
export const isEmpty = (data: any): boolean => {
  if (data === null || data === undefined) return true;
  if (Array.isArray(data)) return data.length === 0;
  if (typeof data === 'object') return Object.keys(data).length === 0;
  if (typeof data === 'string') return data.trim().length === 0;
  return false;
};

/**
 * Debounce function for search and API calls
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Format large numbers with K, M suffixes
 */
export const formatLargeNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};
