import {
  FaShoppingCart,
  FaWallet,
  FaPiggyBank,
  FaMoneyBillWave,
  FaRefresh,
  FaCog,
} from 'react-icons/fa';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import { useCallback, useMemo } from 'react';
import { useDashboardData } from './hooks/useDashboardData';
import { useDashboardActions } from './hooks/useDashboardActions';
import {
  formatCurrency,
  formatDate,
  formatPercentage,
  getTrendColor,
  canCancelBooking,
  getTrendDirection
} from './utils/dashboardUtils';
import { StatsGrid } from './components/StatCard';
import { DashboardSection, TransactionItem, BookingItem } from './components/DashboardSection';

export default function Dashboard() {
  const {
    data,
    loadingStates,
    errors,
    isLoading,
    refresh,
    refreshAll,
    isAuthenticated,
    optimisticBookingUpdate,
    optimisticTransactionUpdate,
    lastRefresh,
    autoRefreshConfig,
    configureAutoRefresh
  } = useDashboardData();

  const {
    handleNavigation,
    handleBookingAction,
    handleTransactionAction,
    handleStatClick
  } = useDashboardActions({
    optimisticBookingUpdate,
    optimisticTransactionUpdate
  });

  // Handle refresh actions
  const handleRefresh = useCallback(async (type?: keyof typeof loadingStates) => {
    if (type) {
      await refresh(type);
    } else {
      await refreshAll();
    }
  }, [refresh, refreshAll]);

  // Generate statistics cards configuration from real data
  const statsConfig = useMemo(() => {
    if (!data.stats) {
      return [];
    }

    return [
      {
        title: 'Total Orders',
        value: data.stats.totalOrders.toString(),
        percentage: data.stats.orderGrowth ? formatPercentage(data.stats.orderGrowth) : '0%',
        color: getTrendColor(data.stats.orderGrowth || 0),
        bg: 'bg-pink-100',
        icon: <FaShoppingCart />,
        trend: getTrendDirection(data.stats.orderGrowth || 0),
        loading: loadingStates.stats,
        error: errors.stats,
      },
      {
        title: 'Total Spend',
        value: formatCurrency(data.stats.totalSpend),
        percentage: data.stats.spendGrowth ? formatPercentage(data.stats.spendGrowth) : '0%',
        color: getTrendColor(data.stats.spendGrowth || 0),
        bg: 'bg-red-100',
        icon: <FaMoneyBillWave />,
        trend: getTrendDirection(data.stats.spendGrowth || 0),
        loading: loadingStates.stats,
        error: errors.stats,
      },
      {
        title: 'Wallet',
        value: formatCurrency(data.stats.walletBalance),
        percentage: data.stats.walletGrowth ? formatPercentage(data.stats.walletGrowth) : '0%',
        color: getTrendColor(data.stats.walletGrowth || 0),
        bg: 'bg-blue-100',
        icon: <FaWallet />,
        trend: getTrendDirection(data.stats.walletGrowth || 0),
        loading: loadingStates.stats,
        error: errors.stats,
      },
      {
        title: 'Total Savings',
        value: formatCurrency(data.stats.totalSavings),
        percentage: data.stats.savingsGrowth ? formatPercentage(data.stats.savingsGrowth) : '0%',
        color: getTrendColor(data.stats.savingsGrowth || 0),
        bg: 'bg-green-100',
        icon: <FaPiggyBank />,
        trend: getTrendDirection(data.stats.savingsGrowth || 0),
        loading: loadingStates.stats,
        error: errors.stats,
      },
    ];
  }, [data.stats, loadingStates.stats, errors.stats]);



  // Show loading state if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
        <div className="text-center py-12">
          <p className="text-gray-600">Please log in to view your dashboard.</p>
        </div>
      </div>
    );
  }



  return (
    <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
      {/* Breadcrumb & Title */}
      <BreadCrumb title="Dashboard" item1="Customer" />
      <div className="flex justify-between items-center mt-4 mb-6">
        <div>
          <h2 className="text-2xl font-semibold text-gray-800">Dashboard</h2>
          {lastRefresh && (
            <p className="text-sm text-gray-500 mt-1">
              Last updated: {lastRefresh.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          {autoRefreshConfig.enabled && (
            <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
              Auto-refresh: {Math.floor(autoRefreshConfig.interval / 60000)}min
            </span>
          )}
          <button
            onClick={() => configureAutoRefresh({ enabled: !autoRefreshConfig.enabled })}
            className={`text-xs px-2 py-1 rounded-full transition-colors ${
              autoRefreshConfig.enabled
                ? 'text-green-600 bg-green-100 hover:bg-green-200'
                : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
            }`}
            title={`${autoRefreshConfig.enabled ? 'Disable' : 'Enable'} auto-refresh`}
          >
            <FaCog className="w-3 h-3" />
          </button>
          <CustomButton
            label="Refresh"
            color="primary"
            variant="light"
            size="sm"
            startContent={<FaRefresh />}
            onPress={() => handleRefresh()}
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* Stats Cards Grid */}
      <StatsGrid
        configs={statsConfig}
        onStatClick={(_, config) => {
          handleStatClick(config.title, config.value);
        }}
      />

      {/* Transactions & Bookings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <DashboardSection
          title="Recent Transactions"
          isLoading={loadingStates.transactions}
          error={errors.transactions}
          isEmpty={data.recentTransactions.length === 0}
          emptyMessage="No recent transactions found"
          emptyIcon={<FaMoneyBillWave />}
          onViewAll={() => handleNavigation('view_all_transactions')}
          onRefresh={() => handleRefresh('transactions')}
        >
          <div className="space-y-3">
            {data.recentTransactions.slice(0, 4).map((transaction, index) => (
              <TransactionItem
                key={transaction.id || `transaction-${index}`}
                type={transaction.type}
                date={formatDate(transaction.date)}
                amount={formatCurrency(transaction.amount, transaction.currency)}
                status={transaction.status}
                onClick={() => handleTransactionAction('view', transaction.id)}
                onStatusClick={() => console.log('Status clicked', transaction.status)}
              />
            ))}
          </div>
        </DashboardSection>

        {/* Recent Bookings */}
        <DashboardSection
          title="Recent Bookings"
          isLoading={loadingStates.bookings}
          error={errors.bookings}
          isEmpty={data.recentBookings.length === 0}
          emptyMessage="No recent bookings found"
          emptyIcon={<FaShoppingCart />}
          onViewAll={() => handleNavigation('view_all_bookings')}
          onRefresh={() => handleRefresh('bookings')}
        >
          <div className="space-y-3">
            {data.recentBookings.slice(0, 4).map((booking, index) => (
              <BookingItem
                key={booking.id || `booking-${index}`}
                service={booking.service}
                date={formatDate(booking.date)}
                customerName={booking.customerName}
                customerEmail={booking.customerEmail}
                amount={booking.amount ? formatCurrency(booking.amount, booking.currency) : undefined}
                status={booking.status}
                canCancel={booking.canCancel || canCancelBooking(booking.status, booking.date)}
                canReschedule={booking.canReschedule}
                onCancel={() => handleBookingAction('cancel', booking.bookingId)}
                onReschedule={() => handleBookingAction('reschedule', booking.bookingId)}
                onClick={() => handleBookingAction('view', booking.bookingId)}
              />
            ))}
          </div>
        </DashboardSection>
      </div>
    </div>
  );
}
