import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { apiClient } from '../../../../api';
import { DashboardAction, DashboardNavigation } from '../types/dashboardTypes';

/**
 * Hook for handling dashboard navigation and actions
 */
export const useDashboardActions = (optimisticUpdates?: {
  optimisticBookingUpdate?: (bookingId: string, newStatus: string, rollbackFn?: () => void) => void;
  optimisticTransactionUpdate?: (transactionId: string, newStatus: string, rollbackFn?: () => void) => void;
}) => {
  const navigate = useNavigate();

  // Navigation handlers
  const handleNavigation = useCallback((action: DashboardAction, data?: any) => {
    switch (action) {
      case 'view_all_transactions':
        // TODO: Replace with actual route when transactions page is implemented
        navigate('/customer/transactions');
        break;
      
      case 'view_all_bookings':
        // TODO: Replace with actual route when bookings page is implemented
        navigate('/customer/bookings');
        break;
      
      case 'view_booking_details':
        if (data?.bookingId) {
          navigate(`/customer/bookings/${data.bookingId}`);
        } else {
          toast.error('Booking ID is required');
        }
        break;
      
      case 'view_transaction_details':
        if (data?.transactionId) {
          navigate(`/customer/transactions/${data.transactionId}`);
        } else {
          toast.error('Transaction ID is required');
        }
        break;
      
      default:
        console.warn(`Unknown navigation action: ${action}`);
        toast.info('This feature is coming soon');
    }
  }, [navigate]);

  // Booking action handlers with optimistic updates
  const handleBookingAction = useCallback(async (action: string, bookingId: string, data?: any) => {
    if (!bookingId) {
      toast.error('Booking ID is required');
      return false;
    }

    try {
      switch (action) {
        case 'cancel':
          // Optimistic update: immediately show as cancelled
          if (optimisticUpdates?.optimisticBookingUpdate) {
            optimisticUpdates.optimisticBookingUpdate(
              bookingId,
              'Cancelled',
              () => toast.error('Failed to cancel booking - status reverted')
            );
          }
          return await cancelBooking(bookingId, data?.reason);

        case 'reschedule':
          // Optimistic update: immediately show as rescheduled
          if (optimisticUpdates?.optimisticBookingUpdate) {
            optimisticUpdates.optimisticBookingUpdate(
              bookingId,
              'Rescheduled',
              () => toast.error('Failed to reschedule booking - status reverted')
            );
          }
          return await rescheduleBooking(bookingId, data?.newDate, data?.newTime);

        case 'view':
          handleNavigation('view_booking_details', { bookingId });
          return true;

        default:
          console.warn(`Unknown booking action: ${action}`);
          toast.error(`Unknown action: ${action}`);
          return false;
      }
    } catch (error: any) {
      console.error(`Error performing booking action ${action}:`, error);
      toast.error(error.message || `Failed to ${action} booking`);
      return false;
    }
  }, [handleNavigation, optimisticUpdates]);

  // Transaction action handlers
  const handleTransactionAction = useCallback(async (action: string, transactionId: string, data?: any) => {
    if (!transactionId) {
      toast.error('Transaction ID is required');
      return false;
    }

    try {
      switch (action) {
        case 'view':
          handleNavigation('view_transaction_details', { transactionId });
          return true;
        
        case 'download_receipt':
          return await downloadTransactionReceipt(transactionId);
        
        case 'dispute':
          return await disputeTransaction(transactionId, data?.reason);
        
        default:
          console.warn(`Unknown transaction action: ${action}`);
          toast.error(`Unknown action: ${action}`);
          return false;
      }
    } catch (error: any) {
      console.error(`Error performing transaction action ${action}:`, error);
      toast.error(error.message || `Failed to ${action} transaction`);
      return false;
    }
  }, [handleNavigation]);

  // Stat card click handlers
  const handleStatClick = useCallback((statType: string, value: any) => {
    switch (statType) {
      case 'Total Orders':
        handleNavigation('view_all_bookings');
        break;
      
      case 'Total Spend':
        handleNavigation('view_all_transactions', { filter: 'spending' });
        break;
      
      case 'Wallet':
        navigate('/customer/wallet');
        break;
      
      case 'Total Savings':
        handleNavigation('view_all_transactions', { filter: 'savings' });
        break;
      
      default:
        console.log(`Clicked on ${statType}:`, value);
        toast.info('Detailed view coming soon');
    }
  }, [navigate, handleNavigation]);

  return {
    handleNavigation,
    handleBookingAction,
    handleTransactionAction,
    handleStatClick
  };
};

// Booking API functions
const cancelBooking = async (bookingId: string, reason?: string): Promise<boolean> => {
  try {
    console.log(`Cancelling booking: ${bookingId}`, { reason });
    
    const response = await apiClient.put(`/api/v1/bookings/${bookingId}/cancel`, {
      reason: reason || 'Customer requested cancellation'
    });
    
    if (response.status === 200) {
      toast.success('Booking cancelled successfully');
      return true;
    }
    
    throw new Error('Failed to cancel booking');
  } catch (error: any) {
    console.error('Error cancelling booking:', error);
    
    // For development, show mock success
    if (process.env.NODE_ENV === 'development') {
      toast.success('Booking cancelled successfully (mock)');
      return true;
    }
    
    throw error;
  }
};

const rescheduleBooking = async (bookingId: string, newDate?: string, newTime?: string): Promise<boolean> => {
  try {
    console.log(`Rescheduling booking: ${bookingId}`, { newDate, newTime });
    
    if (!newDate || !newTime) {
      toast.error('Please select a new date and time');
      return false;
    }
    
    const response = await apiClient.put(`/api/v1/bookings/${bookingId}/reschedule`, {
      newDate,
      newTime
    });
    
    if (response.status === 200) {
      toast.success('Booking rescheduled successfully');
      return true;
    }
    
    throw new Error('Failed to reschedule booking');
  } catch (error: any) {
    console.error('Error rescheduling booking:', error);
    
    // For development, show mock success
    if (process.env.NODE_ENV === 'development') {
      toast.success('Booking rescheduled successfully (mock)');
      return true;
    }
    
    throw error;
  }
};

// Transaction API functions
const downloadTransactionReceipt = async (transactionId: string): Promise<boolean> => {
  try {
    console.log(`Downloading receipt for transaction: ${transactionId}`);
    
    const response = await apiClient.get(`/api/v1/transactions/${transactionId}/receipt`, {
      responseType: 'blob'
    });
    
    if (response.status === 200) {
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `receipt-${transactionId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('Receipt downloaded successfully');
      return true;
    }
    
    throw new Error('Failed to download receipt');
  } catch (error: any) {
    console.error('Error downloading receipt:', error);
    
    // For development, show mock success
    if (process.env.NODE_ENV === 'development') {
      toast.success('Receipt download started (mock)');
      return true;
    }
    
    throw error;
  }
};

const disputeTransaction = async (transactionId: string, reason: string): Promise<boolean> => {
  try {
    console.log(`Disputing transaction: ${transactionId}`, { reason });
    
    if (!reason) {
      toast.error('Please provide a reason for the dispute');
      return false;
    }
    
    const response = await apiClient.post(`/api/v1/transactions/${transactionId}/dispute`, {
      reason
    });
    
    if (response.status === 200) {
      toast.success('Dispute submitted successfully');
      return true;
    }
    
    throw new Error('Failed to submit dispute');
  } catch (error: any) {
    console.error('Error submitting dispute:', error);
    
    // For development, show mock success
    if (process.env.NODE_ENV === 'development') {
      toast.success('Dispute submitted successfully (mock)');
      return true;
    }
    
    throw error;
  }
};
