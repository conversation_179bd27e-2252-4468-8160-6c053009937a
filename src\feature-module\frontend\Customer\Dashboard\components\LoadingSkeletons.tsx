import { Skeleton } from '@heroui/react';

/**
 * Loading skeleton for statistics cards
 */
export const StatsCardSkeleton = () => (
  <div className="bg-white p-4 shadow-md rounded-lg flex items-center justify-between">
    <div className="flex items-center space-x-4 flex-1">
      <Skeleton className="w-12 h-12 rounded-full" />
      <div className="flex-1 space-y-2">
        <Skeleton className="h-4 w-24 rounded" />
        <Skeleton className="h-6 w-16 rounded" />
      </div>
    </div>
    <Skeleton className="h-6 w-12 rounded-full" />
  </div>
);

/**
 * Loading skeleton for transaction items
 */
export const TransactionSkeleton = () => (
  <div className="flex justify-between items-center bg-gray-50 p-4 rounded-lg border border-gray-100">
    <div className="flex-1 space-y-2">
      <Skeleton className="h-5 w-32 rounded" />
      <Skeleton className="h-4 w-40 rounded" />
    </div>
    <div className="flex items-center gap-3">
      <Skeleton className="h-6 w-16 rounded-full" />
      <Skeleton className="h-5 w-20 rounded" />
    </div>
  </div>
);

/**
 * Loading skeleton for booking items
 */
export const BookingSkeleton = () => (
  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 p-4 rounded-lg border border-gray-100">
    <div className="flex items-center space-x-3 md:space-x-4 flex-1">
      <Skeleton className="w-14 h-14 sm:w-16 sm:h-16 rounded-lg" />
      <div className="flex-1 space-y-2">
        <Skeleton className="h-5 w-36 rounded" />
        <Skeleton className="h-4 w-24 rounded" />
        <Skeleton className="h-4 w-28 rounded" />
        <Skeleton className="h-3 w-32 rounded" />
      </div>
    </div>
    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
      <Skeleton className="h-6 w-20 rounded-full" />
      <Skeleton className="h-8 w-16 rounded" />
    </div>
  </div>
);

/**
 * Loading skeleton for the entire stats grid
 */
export const StatsGridSkeleton = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
    {Array.from({ length: 4 }).map((_, index) => (
      <StatsCardSkeleton key={`stats-skeleton-${index}`} />
    ))}
  </div>
);

/**
 * Loading skeleton for transactions section
 */
export const TransactionsSectionSkeleton = () => (
  <div className="bg-white p-5 md:p-6 shadow-md rounded-lg">
    <div className="flex justify-between items-center mb-5">
      <Skeleton className="h-6 w-40 rounded" />
      <Skeleton className="h-8 w-20 rounded" />
    </div>
    <div className="space-y-3">
      {Array.from({ length: 4 }).map((_, index) => (
        <TransactionSkeleton key={`transaction-skeleton-${index}`} />
      ))}
    </div>
  </div>
);

/**
 * Loading skeleton for bookings section
 */
export const BookingsSectionSkeleton = () => (
  <div className="bg-white p-5 md:p-6 shadow-md rounded-lg">
    <div className="flex justify-between items-center mb-5">
      <Skeleton className="h-6 w-36 rounded" />
      <Skeleton className="h-8 w-20 rounded" />
    </div>
    <div className="space-y-3">
      {Array.from({ length: 3 }).map((_, index) => (
        <BookingSkeleton key={`booking-skeleton-${index}`} />
      ))}
    </div>
  </div>
);

/**
 * Loading skeleton for the entire dashboard
 */
export const DashboardSkeleton = () => (
  <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
    {/* Breadcrumb & Title Skeleton */}
    <div className="mb-4">
      <Skeleton className="h-4 w-32 rounded mb-2" />
      <Skeleton className="h-8 w-48 rounded" />
    </div>

    {/* Stats Cards Grid Skeleton */}
    <StatsGridSkeleton />

    {/* Transactions & Bookings Grid Skeleton */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <TransactionsSectionSkeleton />
      <BookingsSectionSkeleton />
    </div>
  </div>
);

/**
 * Error state component
 */
interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}

export const ErrorState = ({ 
  title = "Something went wrong", 
  message = "We couldn't load this data. Please try again.", 
  onRetry,
  showRetry = true 
}: ErrorStateProps) => (
  <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
      <svg 
        className="w-8 h-8 text-red-500" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
        />
      </svg>
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 mb-6 max-w-md">{message}</p>
    {showRetry && onRetry && (
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2"
      >
        <svg 
          className="w-4 h-4" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
          />
        </svg>
        Try Again
      </button>
    )}
  </div>
);

/**
 * Empty state component
 */
interface EmptyStateProps {
  title?: string;
  message?: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const EmptyState = ({ 
  title = "No data available", 
  message = "There's nothing to show here yet.", 
  icon,
  action 
}: EmptyStateProps) => (
  <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
      {icon || (
        <svg 
          className="w-8 h-8 text-gray-400" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" 
          />
        </svg>
      )}
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 mb-6 max-w-md">{message}</p>
    {action && (
      <button
        onClick={action.onClick}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
      >
        {action.label}
      </button>
    )}
  </div>
);
