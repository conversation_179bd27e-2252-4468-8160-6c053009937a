import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from 'react-oidc-context';
import { useCallback, useMemo } from 'react';
import { toast } from 'react-toastify';
import { useDashboardRefresh } from './useDashboardRefresh';
import {
  getDashboardStats,
  getRecentTransactions,
  getRecentBookings,
  getUserProfile,
  refreshDashboardData
} from '../../../../service/dashboardService';
import {
  DashboardData,
  DashboardLoadingStates,
  DashboardErrors,
  DashboardFilters
} from '../types/dashboardTypes';

// Query keys for React Query
export const DASHBOARD_QUERY_KEYS = {
  stats: (userId: string) => ['dashboard', 'stats', userId],
  transactions: (userId: string, filters?: DashboardFilters) => ['dashboard', 'transactions', userId, filters],
  bookings: (userId: string, filters?: DashboardFilters) => ['dashboard', 'bookings', userId, filters],
  profile: (userId: string) => ['dashboard', 'profile', userId],
  all: (userId: string) => ['dashboard', userId]
} as const;

/**
 * Get user ID from auth context
 */
const useUserId = (): string | null => {
  const auth = useAuth();
  
  return useMemo(() => {
    if (!auth.isAuthenticated || !auth.user) {
      return null;
    }
    
    // Try different possible user ID fields
    return (
      auth.user.profile.sub ||
      auth.user.profile.id ||
      auth.user.profile.user_id ||
      auth.user.profile.email ||
      null
    );
  }, [auth.isAuthenticated, auth.user]);
};

/**
 * Hook for fetching dashboard statistics
 */
export const useDashboardStats = () => {
  const userId = useUserId();
  
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.stats(userId || ''),
    queryFn: () => getDashboardStats(userId!),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error: any) => {
      console.error('Error fetching dashboard stats:', error);
      toast.error('Failed to load dashboard statistics');
    }
  });
};

/**
 * Hook for fetching recent transactions
 */
export const useRecentTransactions = (filters?: DashboardFilters) => {
  const userId = useUserId();
  
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.transactions(userId || '', filters),
    queryFn: () => getRecentTransactions(userId!, filters),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error: any) => {
      console.error('Error fetching recent transactions:', error);
      toast.error('Failed to load recent transactions');
    }
  });
};

/**
 * Hook for fetching recent bookings
 */
export const useRecentBookings = (filters?: DashboardFilters) => {
  const userId = useUserId();
  
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.bookings(userId || '', filters),
    queryFn: () => getRecentBookings(userId!, filters),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error: any) => {
      console.error('Error fetching recent bookings:', error);
      toast.error('Failed to load recent bookings');
    }
  });
};

/**
 * Hook for fetching user profile
 */
export const useUserProfile = () => {
  const userId = useUserId();
  
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.profile(userId || ''),
    queryFn: () => getUserProfile(userId!),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error: any) => {
      console.error('Error fetching user profile:', error);
      // Don't show toast for profile errors as it might be handled elsewhere
    }
  });
};

/**
 * Main dashboard data hook that combines all data
 */
export const useDashboardData = (filters?: DashboardFilters) => {
  const queryClient = useQueryClient();
  const userId = useUserId();

  // Initialize refresh functionality
  const {
    refreshAll: refreshAllData,
    refresh: refreshData,
    optimisticBookingUpdate,
    optimisticTransactionUpdate,
    prefetchData,
    configureAutoRefresh,
    getCacheStatus,
    isRefreshing: isRefreshingData,
    lastRefresh,
    autoRefreshConfig
  } = useDashboardRefresh(userId);

  const statsQuery = useDashboardStats();
  const transactionsQuery = useRecentTransactions(filters);
  const bookingsQuery = useRecentBookings(filters);
  const profileQuery = useUserProfile();
  
  // Combined loading states
  const loadingStates: DashboardLoadingStates = useMemo(() => ({
    stats: statsQuery.isLoading,
    transactions: transactionsQuery.isLoading,
    bookings: bookingsQuery.isLoading,
    userProfile: profileQuery.isLoading
  }), [
    statsQuery.isLoading,
    transactionsQuery.isLoading,
    bookingsQuery.isLoading,
    profileQuery.isLoading
  ]);
  
  // Combined error states
  const errors: DashboardErrors = useMemo(() => ({
    stats: statsQuery.error?.message || null,
    transactions: transactionsQuery.error?.message || null,
    bookings: bookingsQuery.error?.message || null,
    userProfile: profileQuery.error?.message || null
  }), [
    statsQuery.error,
    transactionsQuery.error,
    bookingsQuery.error,
    profileQuery.error
  ]);
  
  // Combined data
  const data: DashboardData = useMemo(() => ({
    stats: statsQuery.data || null,
    recentTransactions: transactionsQuery.data || [],
    recentBookings: bookingsQuery.data || [],
    userProfile: profileQuery.data || null
  }), [
    statsQuery.data,
    transactionsQuery.data,
    bookingsQuery.data,
    profileQuery.data
  ]);
  
  // Check if any query is loading (including refresh operations)
  const isLoading = useMemo(() =>
    Object.values(loadingStates).some(loading => loading) || isRefreshingData,
    [loadingStates, isRefreshingData]
  );

  // Check if all queries have errors
  const hasErrors = useMemo(() =>
    Object.values(errors).some(error => error !== null),
    [errors]
  );

  // Enhanced refresh functions that use the refresh hook
  const refreshAll = useCallback(async (options?: { silent?: boolean; force?: boolean }) => {
    await refreshAllData({
      showToast: !options?.silent,
      silent: options?.silent || false,
      force: options?.force || false
    });
  }, [refreshAllData]);

  const refresh = useCallback(async (
    type: keyof DashboardLoadingStates,
    options?: { silent?: boolean; force?: boolean }
  ) => {
    await refreshData(type, {
      showToast: !options?.silent,
      silent: options?.silent || false,
      force: options?.force || false
    });
  }, [refreshData]);
  
  return {
    data,
    loadingStates,
    errors,
    isLoading,
    hasErrors,
    refresh,
    refreshAll,
    userId,
    isAuthenticated: !!userId,
    // Enhanced refresh and caching features
    optimisticBookingUpdate,
    optimisticTransactionUpdate,
    prefetchData,
    configureAutoRefresh,
    getCacheStatus,
    lastRefresh,
    autoRefreshConfig,
    isRefreshingData
  };
};
