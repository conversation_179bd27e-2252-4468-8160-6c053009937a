import { Skeleton } from '@heroui/react';
import { StatCardConfig } from '../types/dashboardTypes';

interface StatCardProps {
  config: StatCardConfig;
  onClick?: () => void;
}

export const StatCard = ({ config, onClick }: StatCardProps) => {
  if (config.loading) {
    return (
      <div className="bg-white p-4 shadow-md rounded-lg flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1">
          <Skeleton className="w-12 h-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-24 rounded" />
            <Skeleton className="h-6 w-16 rounded" />
          </div>
        </div>
        <Skeleton className="h-6 w-12 rounded-full" />
      </div>
    );
  }

  if (config.error) {
    return (
      <div className="bg-white p-4 shadow-md rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <p className="text-xs text-gray-500">Error loading {config.title.toLowerCase()}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white p-4 shadow-md hover:shadow-lg transition-all duration-300 rounded-lg flex items-center justify-between ${
        onClick ? 'cursor-pointer hover:scale-105' : ''
      }`}
      onClick={onClick}
    >
      <div className={`p-3 rounded-full ${config.bg} text-lg flex items-center justify-center`}>
        {config.icon}
      </div>
      <div className="flex-1 ml-4">
        <p className="text-gray-500 text-sm font-medium">{config.title}</p>
        <p className="text-xl font-semibold">{config.value}</p>
      </div>
      {config.percentage && (
        <div className="flex flex-col items-end">
          <span
            className={`px-3 py-1 text-xs font-semibold rounded-full ${config.color} bg-opacity-20 flex items-center gap-1`}
          >
            {config.trend === 'up' && (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
              </svg>
            )}
            {config.trend === 'down' && (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
              </svg>
            )}
            {config.percentage}
          </span>
          <span className="text-xs text-gray-400 mt-1">vs last month</span>
        </div>
      )}
    </div>
  );
};

interface StatsGridProps {
  configs: StatCardConfig[];
  onStatClick?: (index: number, config: StatCardConfig) => void;
}

export const StatsGrid = ({ configs, onStatClick }: StatsGridProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
      {configs.map((config, index) => (
        <StatCard
          key={`stat-${index}`}
          config={config}
          onClick={onStatClick ? () => onStatClick(index, config) : undefined}
        />
      ))}
    </div>
  );
};

// Animated counter component for stat values
interface AnimatedCounterProps {
  value: number;
  duration?: number;
  prefix?: string;
  suffix?: string;
  decimals?: number;
}

export const AnimatedCounter = ({ 
  value, 
  duration = 1000, 
  prefix = '', 
  suffix = '', 
  decimals = 0 
}: AnimatedCounterProps) => {
  const [displayValue, setDisplayValue] = React.useState(0);

  React.useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = value * easeOutQuart;
      
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  return (
    <span>
      {prefix}{displayValue.toFixed(decimals)}{suffix}
    </span>
  );
};

// Import React for the AnimatedCounter component
import React from 'react';
