import { ReactNode } from 'react';
import { Skeleton } from '@heroui/react';
import CustomButton from '../../../../components/CustomButton';
import { FaEye, FaExclamationTriangle, FaRefresh } from 'react-icons/fa';

interface DashboardSectionProps {
  title: string;
  children: ReactNode;
  isLoading?: boolean;
  error?: string | null;
  isEmpty?: boolean;
  emptyMessage?: string;
  emptyIcon?: ReactNode;
  onViewAll?: () => void;
  onRefresh?: () => void;
  viewAllLabel?: string;
  className?: string;
}

export const DashboardSection = ({
  title,
  children,
  isLoading = false,
  error = null,
  isEmpty = false,
  emptyMessage = "No data available",
  emptyIcon,
  onViewAll,
  onRefresh,
  viewAllLabel = "View All",
  className = ""
}: DashboardSectionProps) => {
  return (
    <div className={`bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-5">
        <h3 className="font-bold text-lg text-gray-800">{title}</h3>
        <div className="flex items-center gap-2">
          {onRefresh && (
            <CustomButton
              label=""
              color="primary"
              variant="light"
              size="sm"
              startContent={<FaRefresh />}
              onPress={onRefresh}
              isIconOnly
              className="min-w-8"
            />
          )}
          {onViewAll && (
            <CustomButton
              label={viewAllLabel}
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={onViewAll}
            />
          )}
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <LoadingContent />
      ) : error ? (
        <ErrorContent error={error} onRetry={onRefresh} />
      ) : isEmpty ? (
        <EmptyContent message={emptyMessage} icon={emptyIcon} />
      ) : (
        children
      )}
    </div>
  );
};

// Loading content component
const LoadingContent = () => (
  <div className="space-y-3">
    {Array.from({ length: 3 }).map((_, index) => (
      <div
        key={`loading-${index}`}
        className="flex justify-between items-center bg-gray-50 p-4 rounded-lg border border-gray-100"
      >
        <div className="flex items-center space-x-3 flex-1">
          <Skeleton className="w-12 h-12 rounded-lg" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-32 rounded" />
            <Skeleton className="h-3 w-24 rounded" />
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Skeleton className="h-6 w-16 rounded-full" />
          <Skeleton className="h-4 w-20 rounded" />
        </div>
      </div>
    ))}
  </div>
);

// Error content component
interface ErrorContentProps {
  error: string;
  onRetry?: () => void;
}

const ErrorContent = ({ error, onRetry }: ErrorContentProps) => (
  <div className="text-center py-8">
    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
      <FaExclamationTriangle className="w-8 h-8 text-red-500" />
    </div>
    <h4 className="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h4>
    <p className="text-gray-600 mb-4 max-w-md mx-auto">{error}</p>
    {onRetry && (
      <CustomButton
        label="Try Again"
        color="primary"
        size="sm"
        startContent={<FaRefresh />}
        onPress={onRetry}
      />
    )}
  </div>
);

// Empty content component
interface EmptyContentProps {
  message: string;
  icon?: ReactNode;
}

const EmptyContent = ({ message, icon }: EmptyContentProps) => (
  <div className="text-center py-8">
    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
      {icon || (
        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
      )}
    </div>
    <p className="text-gray-600">{message}</p>
  </div>
);

// Transaction item component
interface TransactionItemProps {
  type: string;
  date: string;
  amount: string;
  status: string;
  onStatusClick?: () => void;
  onClick?: () => void;
}

export const TransactionItem = ({ 
  type, 
  date, 
  amount, 
  status, 
  onStatusClick, 
  onClick 
}: TransactionItemProps) => (
  <div
    className={`flex justify-between items-center bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100 ${
      onClick ? 'cursor-pointer' : ''
    }`}
    onClick={onClick}
  >
    <div className="flex-1">
      <h4 className="text-base md:text-lg font-medium">{type}</h4>
      <p className="text-sm text-gray-500">{date}</p>
    </div>
    <div className="flex items-center gap-3">
      <div
        className={`px-3 py-1 text-xs font-semibold rounded-full ${
          onClick ? 'cursor-pointer hover:opacity-80' : ''
        }`}
        onClick={onStatusClick}
      >
        {status}
      </div>
      <span className="font-semibold text-gray-700 text-lg">{amount}</span>
    </div>
  </div>
);

// Booking item component
interface BookingItemProps {
  service: string;
  date: string;
  customerName?: string;
  customerEmail?: string;
  amount?: string;
  status: string;
  onCancel?: () => void;
  onReschedule?: () => void;
  onClick?: () => void;
  canCancel?: boolean;
  canReschedule?: boolean;
}

export const BookingItem = ({
  service,
  date,
  customerName,
  customerEmail,
  amount,
  status,
  onCancel,
  onReschedule,
  onClick,
  canCancel = false,
  canReschedule = false
}: BookingItemProps) => (
  <div
    className={`flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100 ${
      onClick ? 'cursor-pointer' : ''
    }`}
    onClick={onClick}
  >
    <div className="flex items-center space-x-3 md:space-x-4 flex-1">
      <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      </div>
      <div className="flex-1">
        <h4 className="text-base md:text-lg font-medium">{service}</h4>
        <p className="text-sm text-gray-500">{date}</p>
        {customerName && (
          <p className="text-sm text-gray-600 font-medium">{customerName}</p>
        )}
        {customerEmail && (
          <p className="text-xs text-gray-500">{customerEmail}</p>
        )}
        {amount && (
          <p className="text-sm font-semibold text-green-600">{amount}</p>
        )}
      </div>
    </div>
    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
      <div className="px-3 py-1 text-xs font-semibold rounded-full">
        {status}
      </div>
      <div className="flex gap-2">
        {canReschedule && onReschedule && (
          <CustomButton
            color="warning"
            label="Reschedule"
            size="sm"
            variant="light"
            onPress={onReschedule}
          />
        )}
        {canCancel && onCancel && (
          <CustomButton
            color="danger"
            label="Cancel"
            size="sm"
            variant="light"
            onPress={onCancel}
          />
        )}
      </div>
    </div>
  </div>
);
