import { useCallback, useEffect, useRef, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { DASHBOARD_QUERY_KEYS } from './useDashboardData';
import { DashboardLoadingStates } from '../types/dashboardTypes';

interface RefreshOptions {
  showToast?: boolean;
  silent?: boolean;
  force?: boolean;
}

interface AutoRefreshConfig {
  enabled: boolean;
  interval: number; // in milliseconds
  onlyWhenVisible: boolean;
}

/**
 * Hook for managing dashboard data refresh and caching strategies
 */
export const useDashboardRefresh = (userId: string | null) => {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [autoRefreshConfig, setAutoRefreshConfig] = useState<AutoRefreshConfig>({
    enabled: true,
    interval: 5 * 60 * 1000, // 5 minutes
    onlyWhenVisible: true
  });
  
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isVisibleRef = useRef(true);

  // Track page visibility
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
      
      if (isVisibleRef.current && autoRefreshConfig.enabled) {
        // Refresh data when page becomes visible after being hidden
        refreshAll({ silent: true });
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [autoRefreshConfig.enabled]);

  // Auto refresh setup
  useEffect(() => {
    if (!autoRefreshConfig.enabled || !userId) {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
      return;
    }

    const scheduleRefresh = () => {
      refreshTimeoutRef.current = setTimeout(() => {
        if (autoRefreshConfig.onlyWhenVisible && !isVisibleRef.current) {
          // Skip refresh if page is not visible, but reschedule
          scheduleRefresh();
          return;
        }
        
        refreshAll({ silent: true }).finally(() => {
          scheduleRefresh();
        });
      }, autoRefreshConfig.interval);
    };

    scheduleRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [autoRefreshConfig, userId]);

  // Refresh all dashboard data
  const refreshAll = useCallback(async (options: RefreshOptions = {}) => {
    if (!userId || isRefreshing) return;

    const { showToast = true, silent = false, force = false } = options;

    try {
      setIsRefreshing(true);
      
      if (!silent && showToast) {
        toast.info('Refreshing dashboard data...');
      }

      // Invalidate all dashboard queries
      await queryClient.invalidateQueries({
        queryKey: DASHBOARD_QUERY_KEYS.all(userId),
        exact: false
      });

      // If force refresh, also remove from cache
      if (force) {
        queryClient.removeQueries({
          queryKey: DASHBOARD_QUERY_KEYS.all(userId),
          exact: false
        });
      }

      setLastRefresh(new Date());
      
      if (!silent && showToast) {
        toast.success('Dashboard data refreshed');
      }
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
      if (!silent) {
        toast.error('Failed to refresh dashboard data');
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [userId, queryClient, isRefreshing]);

  // Refresh specific data type
  const refresh = useCallback(async (
    type: keyof DashboardLoadingStates, 
    options: RefreshOptions = {}
  ) => {
    if (!userId || isRefreshing) return;

    const { showToast = true, silent = false, force = false } = options;

    try {
      setIsRefreshing(true);

      let queryKey: any[];
      switch (type) {
        case 'stats':
          queryKey = DASHBOARD_QUERY_KEYS.stats(userId);
          break;
        case 'transactions':
          queryKey = DASHBOARD_QUERY_KEYS.transactions(userId);
          break;
        case 'bookings':
          queryKey = DASHBOARD_QUERY_KEYS.bookings(userId);
          break;
        case 'userProfile':
          queryKey = DASHBOARD_QUERY_KEYS.profile(userId);
          break;
        default:
          throw new Error(`Unknown refresh type: ${type}`);
      }

      if (force) {
        queryClient.removeQueries({ queryKey });
      }

      await queryClient.invalidateQueries({ queryKey });

      if (!silent && showToast) {
        toast.success(`${type} refreshed`);
      }
    } catch (error) {
      console.error(`Error refreshing ${type}:`, error);
      if (!silent) {
        toast.error(`Failed to refresh ${type}`);
      }
    } finally {
      setIsRefreshing(false);
    }
  }, [userId, queryClient, isRefreshing]);

  // Optimistic update for booking status
  const optimisticBookingUpdate = useCallback((
    bookingId: string, 
    newStatus: string, 
    rollbackFn?: () => void
  ) => {
    if (!userId) return;

    const queryKey = DASHBOARD_QUERY_KEYS.bookings(userId);
    
    // Get current data
    const currentData = queryClient.getQueryData(queryKey);
    
    if (currentData && Array.isArray(currentData)) {
      // Update the booking status optimistically
      const updatedData = currentData.map((booking: any) => 
        booking.id === bookingId || booking.bookingId === bookingId
          ? { ...booking, status: newStatus }
          : booking
      );
      
      // Set the optimistic data
      queryClient.setQueryData(queryKey, updatedData);
      
      // Schedule a rollback if provided
      if (rollbackFn) {
        setTimeout(() => {
          const latestData = queryClient.getQueryData(queryKey);
          if (latestData === updatedData) {
            // Data hasn't been updated by a real API call, so rollback
            queryClient.setQueryData(queryKey, currentData);
            rollbackFn();
          }
        }, 10000); // Rollback after 10 seconds if no real update
      }
    }
  }, [userId, queryClient]);

  // Optimistic update for transaction status
  const optimisticTransactionUpdate = useCallback((
    transactionId: string, 
    newStatus: string, 
    rollbackFn?: () => void
  ) => {
    if (!userId) return;

    const queryKey = DASHBOARD_QUERY_KEYS.transactions(userId);
    
    // Get current data
    const currentData = queryClient.getQueryData(queryKey);
    
    if (currentData && Array.isArray(currentData)) {
      // Update the transaction status optimistically
      const updatedData = currentData.map((transaction: any) => 
        transaction.id === transactionId
          ? { ...transaction, status: newStatus }
          : transaction
      );
      
      // Set the optimistic data
      queryClient.setQueryData(queryKey, updatedData);
      
      // Schedule a rollback if provided
      if (rollbackFn) {
        setTimeout(() => {
          const latestData = queryClient.getQueryData(queryKey);
          if (latestData === updatedData) {
            // Data hasn't been updated by a real API call, so rollback
            queryClient.setQueryData(queryKey, currentData);
            rollbackFn();
          }
        }, 10000); // Rollback after 10 seconds if no real update
      }
    }
  }, [userId, queryClient]);

  // Prefetch related data
  const prefetchData = useCallback(async (type: 'transactions' | 'bookings') => {
    if (!userId) return;

    try {
      switch (type) {
        case 'transactions':
          await queryClient.prefetchQuery({
            queryKey: DASHBOARD_QUERY_KEYS.transactions(userId, { limit: 10 }),
            staleTime: 2 * 60 * 1000 // 2 minutes
          });
          break;
        case 'bookings':
          await queryClient.prefetchQuery({
            queryKey: DASHBOARD_QUERY_KEYS.bookings(userId, { limit: 10 }),
            staleTime: 2 * 60 * 1000 // 2 minutes
          });
          break;
      }
    } catch (error) {
      console.error(`Error prefetching ${type}:`, error);
    }
  }, [userId, queryClient]);

  // Configure auto refresh
  const configureAutoRefresh = useCallback((config: Partial<AutoRefreshConfig>) => {
    setAutoRefreshConfig(prev => ({ ...prev, ...config }));
  }, []);

  // Get cache status
  const getCacheStatus = useCallback(() => {
    if (!userId) return null;

    const statsQuery = queryClient.getQueryState(DASHBOARD_QUERY_KEYS.stats(userId));
    const transactionsQuery = queryClient.getQueryState(DASHBOARD_QUERY_KEYS.transactions(userId));
    const bookingsQuery = queryClient.getQueryState(DASHBOARD_QUERY_KEYS.bookings(userId));
    const profileQuery = queryClient.getQueryState(DASHBOARD_QUERY_KEYS.profile(userId));

    return {
      stats: {
        isFresh: statsQuery?.isStale === false,
        lastFetched: statsQuery?.dataUpdatedAt ? new Date(statsQuery.dataUpdatedAt) : null
      },
      transactions: {
        isFresh: transactionsQuery?.isStale === false,
        lastFetched: transactionsQuery?.dataUpdatedAt ? new Date(transactionsQuery.dataUpdatedAt) : null
      },
      bookings: {
        isFresh: bookingsQuery?.isStale === false,
        lastFetched: bookingsQuery?.dataUpdatedAt ? new Date(bookingsQuery.dataUpdatedAt) : null
      },
      profile: {
        isFresh: profileQuery?.isStale === false,
        lastFetched: profileQuery?.dataUpdatedAt ? new Date(profileQuery.dataUpdatedAt) : null
      }
    };
  }, [userId, queryClient]);

  return {
    refreshAll,
    refresh,
    optimisticBookingUpdate,
    optimisticTransactionUpdate,
    prefetchData,
    configureAutoRefresh,
    getCacheStatus,
    isRefreshing,
    lastRefresh,
    autoRefreshConfig
  };
};
